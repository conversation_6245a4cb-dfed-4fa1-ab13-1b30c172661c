'use client';

import { useState, useEffect, useRef } from 'react';
import { Globe } from 'lucide-react';

const LocationBadge = () => {
  const [locationData, setLocationData] = useState({
    country: '',
    countryCode: '',
    city: '',
    time: '',
    loading: true,
    error: false
  });

  // Usar useRef para almacenar la zona horaria entre renderizados
  const timezoneRef = useRef(null);

  // Efecto para establecer Acceso Global
  useEffect(() => {
    // Establecer Acceso Global directamente
    setLocationData({
      country: 'Global',
      countryCode: 'global',
      city: '',
      time: '',
      loading: false,
      error: false
    });
    // No intentamos obtener la ubicación real, siempre mostramos Acceso Global

  }, []);

  return (
    <div className="flex items-center justify-center gap-2 bg-card/40 backdrop-blur-sm border border-white/5 rounded-full px-4 py-1.5 text-sm">
      <div className="w-5 h-5 overflow-hidden rounded-full flex-shrink-0 border border-white/10 flex items-center justify-center bg-primary/10">
        <Globe className="h-3 w-3 text-[#9333ea]" />
      </div>
      <div className="flex items-center">
        <span className="text-foreground/80">Acceso Global</span>
      </div>
    </div>
  );
};

export default LocationBadge;
