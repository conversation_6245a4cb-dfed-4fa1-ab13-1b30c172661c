"use client";

import { useTheme } from "@/contexts/ThemeContext";
import Link from "next/link";
import Image from "next/image";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

const Logo = ({ className = "", size = "md" }: LogoProps) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  // Tamaños basados en el prop size
  const sizes = {
    sm: "text-xl",
    md: "text-2xl",
    lg: "text-3xl",
  };

  // Determinar el color basado en el tema
  const textColor = isDark ? "text-white" : "text-gray-900";

  // Tamaños del logo SVG basados en el prop size (estilo Grammarly)
  const logoSizes = {
    sm: { width: 24, height: 24 },
    md: { width: 28, height: 28 },
    lg: { width: 32, height: 32 },
  };

  return (
    <Link href="/" className={`${className}`}>
      <div className="flex items-center gap-2">
        {/* Logo SVG - estilo Grammarly */}
        <div className="relative flex-shrink-0">
          <Image
            src="/logo/isotipo.png"
            alt="Flasti Logo"
            width={logoSizes[size].width}
            height={logoSizes[size].height}
            className="object-contain"
            priority
          />
        </div>

        {/* Logo de texto estilo Grammarly */}
        <span
          className={`${textColor} text-lg sm:text-xl md:text-2xl`}
          style={{
            fontFamily: "'Söhne', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            fontWeight: 600,
            letterSpacing: '-0.01em'
          }}
        >
          flasti
        </span>
      </div>
    </Link>
  );
};

export default Logo;
