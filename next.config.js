/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Desactivar completamente todos los indicadores de desarrollo
  onDemandEntries: {
    // Período en ms en el que la página se mantendrá en memoria
    maxInactiveAge: 25 * 1000,
    // Número de páginas que se mantendrán en memoria
    pagesBufferLength: 2,
  },
  devIndicators: {
    buildActivity: false,
    buildActivityPosition: 'bottom-right',
  },
  // Configurar para usar Server-Side Rendering con salida standalone
  output: 'standalone',
  distDir: '.next',
  // Desactivar la generación estática de páginas dinámicas
  staticPageGenerationTimeout: 1000,
  // Mover skipTrailingSlashRedirect a la raíz de la configuración
  skipTrailingSlashRedirect: true,
  // Ignorar errores específicos durante la exportación estática
  experimental: {
    // Esto permite que la compilación continúe incluso con errores
    // Mejorar la hidratación para evitar errores
    optimizeServerReact: true,
    // Mejorar la estabilidad de la hidratación
    ppr: false
  }
};

module.exports = nextConfig;
